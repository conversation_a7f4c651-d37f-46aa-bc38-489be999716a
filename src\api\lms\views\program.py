from rest_framework import viewsets
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from services.google.classroom import GoogleClassroomManager
from core.models import (
    Offering,
    StudentEnrollment,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from api.permissions import IsStaffUser
from api.lms.serializers.enrollment import (
    LmsStudentEnrollmentListSerializer,
)
from api.lms.serializers.program import (
    LmsProgramSerializer,
    LmsTeamChannelSerializer,
    LmsStudentEnrollmentRetrieveSerializer,
)
from api.paginations import StandardResultsPagination
from rest_framework.response import Response
from rest_framework.decorators import action
from django.conf import settings
from api.crm.services.evolution_api import EvolutionAPIClient
from services.cache.redis import CacheManager
from rest_framework import status

import logging

logger = logging.getLogger(__name__)


class LmsProgramViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    swagger_tags = ["Programs"]

    queryset = Offering.objects.filter(
        stage__in=[Offering.ENROLLMENT_STAGE, Offering.ENROLLMENT_CLOSED_STAGE],
        deleted=False,
    ).order_by("-created_at")
    serializer_class = LmsProgramSerializer
    pagination_class = StandardResultsPagination
    # authentication_classes = [TokenAuthentication]
    # permission_classes = [IsAuthenticated, IsStaffUser]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.instance_name = settings.EVOLUTION_API_INSTANCE_NAME
        self.evo_client = EvolutionAPIClient(self.instance_name)

        self.cache_manager = CacheManager("lms_programs")
        self.cache_timeout = 60 * 15  # 15 minutes

    def _is_evo_service_available(self):
        try:
            info = self.evo_client.info.get_info()
            return info["data"].get("status", None) == 200
        except Exception as e:
            logger.error(f"Error al verificar el estado del servicio de Evolution: {e}")
            return False

    """
    List enrollments in the external classroom system
    """

    @action(
        detail=True,
        methods=["get"],
        serializer_class=LmsStudentEnrollmentRetrieveSerializer,
    )
    def list_enrollments(self, request, pk=None):
        try:
            offering = self.get_object()

            # Enrollments en nuestro sistema para este offering (programa)
            enrollments = (
                StudentEnrollment.objects.filter(
                    order_item__offering=offering, deleted=False
                )
                .select_related("user", "order_item")
                .order_by("-created_at")
            )

            enrollments_count = enrollments.count()
            accepted_invitation_count = None

            # Agregar campo accepted_invitation a cada enrollment
            for enrollment in enrollments:
                enrollment.accepted_invitation = None

            if not offering.ext_reference:
                # Si no hay referencia externa, retornar solo los enrollments sin datos de classroom
                offering.enrollments = enrollments
                offering.enrollments_count = enrollments_count
                offering.accepted_invitation_count = accepted_invitation_count

                serializer = LmsStudentEnrollmentRetrieveSerializer(offering)
                return Response(serializer.data, status=status.HTTP_200_OK)

            # Intentar obtener datos de classroom con cache
            cache_key = f"classroom_students_{offering.ext_reference}"
            classroom_students = self.cache_manager.get(cache_key)

            if classroom_students is None:
                try:
                    classroom_instance = GoogleClassroomManager()
                    course = classroom_instance.get_course_by_enrollment_code(
                        offering.ext_reference
                    )

                    if course:
                        classroom_students = classroom_instance.list_course_students(
                            course.get("id")
                        )
                        # Cache por 5 minutos
                        self.cache_manager.set(
                            cache_key, classroom_students, timeout=300
                        )
                    else:
                        classroom_students = []
                        # Cache resultado vacío por menos tiempo (1 minuto)
                        self.cache_manager.set(
                            cache_key, classroom_students, timeout=60
                        )

                except Exception as e:
                    logger.error(f"Error al obtener estudiantes de classroom: {e}")
                    classroom_students = None

            # Si obtuvimos datos de classroom, procesar las invitaciones aceptadas
            if classroom_students is not None:
                # Extraer emails de classroom
                classroom_emails = set()
                for student in classroom_students:
                    email = student.get("profile", {}).get("emailAddress")
                    if email:
                        classroom_emails.add(email.lower())

                # Marcar enrollments con invitación aceptada
                accepted_count = 0
                for enrollment in enrollments:
                    user_email = (
                        enrollment.user.email.lower() if enrollment.user.email else ""
                    )
                    enrollment.accepted_invitation = user_email in classroom_emails
                    if enrollment.accepted_invitation:
                        accepted_count += 1

                accepted_invitation_count = accepted_count

            # Crear objeto mock para el serializer
            offering.enrollments = enrollments
            offering.enrollments_count = enrollments_count
            offering.accepted_invitation_count = accepted_invitation_count

            serializer = LmsStudentEnrollmentRetrieveSerializer(offering)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error al obtener enrollments: {e}")
            return Response(
                {
                    "message": "Ocurrió un error al obtener los enrollments",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    """
    Get channel from offering team_channel_id, currently using Evolution API for WhatsApp Groups
    """

    @action(detail=True, methods=["get"], serializer_class=LmsTeamChannelSerializer)
    def get_team_channel(self, request, pk=None):
        try:
            offering = self.get_object()

            if not offering.team_channel_id:
                return Response(
                    {
                        "message": "Este curso no tiene un grupo asignado.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not self._is_evo_service_available():
                return Response(
                    {
                        "message": "El servicio de WhatsApp no se encuentra disponible en este momento.",
                    },
                    status=status.HTTP_424_FAILED_DEPENDENCY,
                )

            response = self.evo_client.groups.fetch_by_jid(offering.team_channel_id)
            group_data = response.get("data")

            mapped_group = {
                "id": group_data.get("id"),
                "subject": group_data.get("subject", None),
                "subject_time": group_data.get("subjectTime", None),
                "description": group_data.get("desc", None),
                "picture_url": group_data.get("pictureUrl", None),
                "size": group_data.get("size", None),
                "creation": group_data.get("creation", None),
            }

            return Response(
                LmsTeamChannelSerializer(mapped_group).data,
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Error al obtener el canal de comunicación: {e}")
            return Response(
                {
                    "message": "Ocurrió un error al obtener el canal de comunicación",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
