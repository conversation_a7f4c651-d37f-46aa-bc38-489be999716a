from rest_framework import viewsets
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from services.google.classroom import GoogleClassroomManager
from core.models import (
    Offering,
    StudentEnrollment,
)
from api.mixins import AuditMixin, SwaggerTagMixin
from api.permissions import IsStaffUser
from api.lms.serializers.enrollment import (
    LmsStudentEnrollmentListSerializer,
)
from api.lms.serializers.program import (
    LmsProgramSerializer,
    LmsTeamChannelSerializer,
    LmsStudentEnrollmentListSerializer,
)
from api.paginations import StandardResultsPagination
from rest_framework.response import Response
from rest_framework.decorators import action
from django.conf import settings
from api.crm.services.evolution_api import EvolutionAPIClient
from services.cache.redis import CacheManager
from rest_framework import status

import logging

logger = logging.getLogger(__name__)


class LmsProgramViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    swagger_tags = ["Programs"]

    queryset = Offering.objects.filter(
        stage__in=[Offering.ENROLLMENT_STAGE, Offering.ENROLLMENT_CLOSED_STAGE],
        deleted=False,
    ).order_by("-created_at")
    serializer_class = LmsProgramSerializer
    pagination_class = StandardResultsPagination
    # authentication_classes = [TokenAuthentication]
    # permission_classes = [IsAuthenticated, IsStaffUser]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.instance_name = settings.EVOLUTION_API_INSTANCE_NAME
        self.evo_client = EvolutionAPIClient(self.instance_name)

        self.cache_manager = CacheManager("lms_programs")
        self.cache_timeout = 60 * 15  # 15 minutes

    def _is_evo_service_available(self):
        try:
            info = self.evo_client.info.get_info()
            return info["data"].get("status", None) == 200
        except Exception as e:
            logger.error(f"Error al verificar el estado del servicio de Evolution: {e}")
            return False

    """
    List enrollments in the external classroom system
    """

    @action(
        detail=True,
        methods=["get"],
        serializer_class=LmsStudentEnrollmentListSerializer,
    )
    def list_enrollments(self, request, pk=None):
        try:
            offering = self.get_object()

            # Enrollments en nuestro sistema para este offering (programa)
            enrollments = StudentEnrollment.objects.filter(
                offering=offering, deleted=False
            ).order_by("-created_at")

            if not offering.ext_reference:
                return Response(
                    {
                        "message": "Este curso no tiene un ID de referencia en el sistema externo.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            classroom_instance = GoogleClassroomManager()
            course = classroom_instance.get_course_by_enrollment_code(
                offering.ext_reference
            )

            if not course:
                return Response(
                    {
                        "message": "No se encontró el curso con el código de inscripción proporcionado.",
                    },
                    status=status.HTTP_404_NOT_FOUND,
                )

            enrollments = classroom_instance.list_course_students(course.get("id"))

            # mapped_enrollments = [
            #     {
            #         "course_id": enrollment.get("courseId"),
            #         "user_id": enrollment.get("userId"),
            #         "profile": {
            #             "id": enrollment.get("profile", {}).get("id"),
            #             "full_name": enrollment.get("profile", {})
            #             .get("name", {})
            #             .get("fullName", ""),
            #             "email_address": enrollment.get("profile", {}).get(
            #                 "emailAddress", ""
            #             ),
            #         },
            #     }
            #     for enrollment in enrollments
            # ]

        except Exception as e:
            logger.error(f"Error al obtener el canal de comunicación: {e}")
            return Response(
                {
                    "message": "Ocurrió un error al obtener el canal de comunicación",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    """
    Get channel from offering team_channel_id, currently using Evolution API for WhatsApp Groups
    """

    @action(detail=True, methods=["get"], serializer_class=LmsTeamChannelSerializer)
    def get_team_channel(self, request, pk=None):
        try:
            offering = self.get_object()

            if not offering.team_channel_id:
                return Response(
                    {
                        "message": "Este curso no tiene un grupo asignado.",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if not self._is_evo_service_available():
                return Response(
                    {
                        "message": "El servicio de WhatsApp no se encuentra disponible en este momento.",
                    },
                    status=status.HTTP_424_FAILED_DEPENDENCY,
                )

            response = self.evo_client.groups.fetch_by_jid(offering.team_channel_id)
            group_data = response.get("data")

            mapped_group = {
                "id": group_data.get("id"),
                "subject": group_data.get("subject", None),
                "subject_time": group_data.get("subjectTime", None),
                "description": group_data.get("desc", None),
                "picture_url": group_data.get("pictureUrl", None),
                "size": group_data.get("size", None),
                "creation": group_data.get("creation", None),
            }

            return Response(
                LmsTeamChannelSerializer(mapped_group).data,
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Error al obtener el canal de comunicación: {e}")
            return Response(
                {
                    "message": "Ocurrió un error al obtener el canal de comunicación",
                },
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
