from rest_framework import viewsets
from rest_framework.decorators import action
from rest_framework.response import Response
from django.http import HttpResponse
from services.documents import DocumentGenerator
from rest_framework.permissions import IsAuthenticated
from django.conf import settings

class DocumentViewSet(viewsets.ViewSet):
    """
    ViewSet for generating test PDF documents.
    Only available in the development environment.
    """
    permission_classes = []  # Temporarily disabled for testing
    
    def get_permissions(self):
        """
        Only available in development.
        """
        if not settings.DEBUG:
            return []
        return super().get_permissions()
    
    @action(detail=False, methods=['post'])
    def certificate(self, request):
        """
        Generates a test certificate.
        """
        data = request.data
        pdf_content = DocumentGenerator.create_certificate(
            student_name=data.get('student_name', '<PERSON>'),
            program_name=data.get('program_name', 'Programa de Especialización en Finanzas Avanzadas 2024'),
            duration=data.get('duration', '120'),
            date=data.get('date', '6 de abril de 2024 al 22 de setiembre de 2024'),
            signatures={
                'Gerardo Inti Lobato': 'Gerente General',
                'Sofia Antaurco Paucar': 'Sub-Gerente'
            }
        )
        
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="certificate.pdf"'
        return response
    
    @action(detail=False, methods=['post'])
    def payment_receipt(self, request):
        """
        Generate a test payment receipt.
        """
        data = request.data
        pdf_content = DocumentGenerator.create_payment_receipt(
            receipt_number=data.get('receipt_number', '000152'),
            student_name=data.get('student_name', 'Carlos Garcia'),
            amount=data.get('amount', 1250.00),
            concept=data.get('concept', 'Programa de Especialización Online'),
            date=data.get('date', '18/04/2024'),
            address=data.get('address', 'M2.8 Lote 08 A.H. Las Lomas de Tab Villa Maria del Triunfo, Lima')
        )
        
        response = HttpResponse(pdf_content, content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename="receipt.pdf"'
        return response
