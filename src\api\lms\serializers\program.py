import json
from rest_framework import serializers
from core.models import Topic, ModuleCourse, Offering, OfferingModule, StudentEnrollment
from api.shared.serializers.file import FileSerializer
from api.lms.serializers.enrollment import (
    LmsStudentEnrollmentBaseSerializer,
    LmsStudentEnrollmentUserSerializer,
    LmsStudentEnrollmentOfferingSerializer,
    LmsStudentEnrollmentOrderItemSerializer,
)

# validation error import
from rest_framework.exceptions import ValidationError


class LmsTeamChannelSerializer(serializers.Serializer):
    id = serializers.CharField(
        read_only=True,
    )
    subject = serializers.CharField()
    subject_time = serializers.IntegerField()
    description = serializers.CharField(allow_null=True, allow_blank=True)
    picture_url = serializers.CharField(allow_null=True, allow_blank=True)
    size = serializers.IntegerField()
    creation = serializers.IntegerField()


class LmsTopicSerializer(serializers.ModelSerializer):
    key = serializers.UUIDField(source="tid", read_only=True)

    class Meta:
        model = Topic
        fields = [
            "key",
            "tid",
            "title",
        ]


class LmsModuleCourseSerializer(serializers.ModelSerializer):
    topics = LmsTopicSerializer(many=True, read_only=True)
    key = serializers.UUIDField(source="mcid", read_only=True)

    class Meta:
        model = ModuleCourse
        fields = [
            "key",
            "mcid",
            "title",
            "topics",
        ]


class LmsProgramModuleSerializer(serializers.ModelSerializer):
    courses = LmsModuleCourseSerializer(many=True, read_only=True)
    key = serializers.UUIDField(source="omid", read_only=True)

    class Meta:
        model = OfferingModule
        fields = [
            "key",
            "omid",
            "title",
            "courses",
        ]


class LmsProgramSerializer(serializers.ModelSerializer):
    thumbnail = FileSerializer(read_only=True, allow_null=True)

    class Meta:
        model = Offering
        fields = [
            "oid",
            "thumbnail",
            "name",
            "long_name",
            "code_name",
            "start_date",
            "end_date",
            "description",
            "duration",
            "frequency",
            "hours",
            "schedule",
            "modality",
            "type",
            "stage",
            "format",
            "ext_reference",
            "team_channel_id",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "oid",
            "thumbnail",
            "created_at",
            "updated_at",
            "stage",
        ]


class LmsRetrieveProgramSerializer(LmsProgramSerializer):
    modules = LmsProgramModuleSerializer(many=True, read_only=True)

    class Meta(LmsProgramSerializer.Meta):
        fields = LmsProgramSerializer.Meta.fields + [
            "modules",
            # "objectives",
        ]


class LmsUpdateProgramSerializer(LmsProgramSerializer):
    modules = serializers.CharField(write_only=True, required=False)
    instructors = serializers.CharField(write_only=True, required=False)

    def validate_modules(self, value):
        """
        Validate and parse the modules JSON string
        """
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            raise ValidationError("Invalid JSON format for modules")
        except TypeError:
            return None

    def update(self, instance, validated_data):
        # instructors_data = validated_data.pop("instructors", None)
        modules_data = validated_data.pop("modules", None)

        offering: Offering = super().update(instance, validated_data)

        if modules_data is not None:
            self._handle_modules_update(offering, modules_data)

        # if instructors_data is not None:
        #     self._handle_instructors_update(offering, instructors_data)

        offering.save()
        return offering

    # def _handle_instructors_update(self, offering, instructors_data):
    #     if isinstance(instructors_data, str):
    #         instructors_data = json.loads(instructors_data)
    #     new_instructor_ids = set(instructor["iid"] for instructor in instructors_data)

    #     new_instructor_ids = [instructor["iid"] for instructor in instructors_data]
    #     offering.instructors.set(Instructor.objects.filter(iid__in=new_instructor_ids))

    def _handle_modules_update(self, offering, modules_data):
        # Get existing modules
        existing_modules = {
            str(module.omid): module for module in offering.modules.all()
        }
        processed_modules = set()

        for module_data in modules_data:
            module_id = module_data.get("omid")

            if module_id and module_id in existing_modules:
                # Update existing module
                module = existing_modules[module_id]
                module.title = module_data.get("title", module.title)
                module.save()
                processed_modules.add(module_id)

                # Handle courses for this module
                self._handle_courses_update(module, module_data.get("courses", []))
            else:
                # Create new module
                new_module = OfferingModule.objects.create(
                    offering=offering, title=module_data.get("title")
                )
                self._handle_courses_update(new_module, module_data.get("courses", []))

        # Delete modules that weren't in the update data
        modules_to_delete = set(existing_modules.keys()) - processed_modules
        OfferingModule.objects.filter(omid__in=modules_to_delete).delete()

    def _handle_courses_update(self, module, courses_data):
        existing_courses = {str(course.mcid): course for course in module.courses.all()}
        processed_courses = set()

        for course_data in courses_data:
            course_id = course_data.get("mcid")

            if course_id and course_id in existing_courses:
                # Update existing course
                course = existing_courses[course_id]
                course.title = course_data.get("title", course.title)
                course.save()
                processed_courses.add(course_id)

                # Handle topics for this course
                self._handle_topics_update(course, course_data.get("topics", []))
            else:
                # Create new course
                new_course = ModuleCourse.objects.create(
                    module=module, title=course_data.get("title")
                )
                self._handle_topics_update(new_course, course_data.get("topics", []))

        # Delete courses that weren't in the update data
        courses_to_delete = set(existing_courses.keys()) - processed_courses
        ModuleCourse.objects.filter(mcid__in=courses_to_delete).delete()

    def _handle_topics_update(self, course, topics_data):
        existing_topics = {str(topic.tid): topic for topic in course.topics.all()}
        processed_topics = set()

        for topic_data in topics_data:
            topic_id = topic_data.get("tid")

            if topic_id and topic_id in existing_topics:
                # Update existing topic
                topic = existing_topics[topic_id]
                topic.title = topic_data.get("title", topic.title)
                topic.save()
                processed_topics.add(topic_id)
            else:
                # Create new topic
                Topic.objects.create(course=course, title=topic_data.get("title"))

        # Delete topics that weren't in the update data
        topics_to_delete = set(existing_topics.keys()) - processed_topics
        Topic.objects.filter(tid__in=topics_to_delete).delete()

    class Meta(LmsProgramSerializer.Meta):
        fields = LmsProgramSerializer.Meta.fields + [
            "modules",
            "instructors",
            "ext_reference",
        ]


"""
Serializers to retrieve additional information such as enrollments
in the external classroom system.
"""


class LmsStudentEnrollmentListSerializer(LmsStudentEnrollmentBaseSerializer):
    """Serializer for listing student enrollments with minimal fields"""

    user = LmsStudentEnrollmentUserSerializer(read_only=True)
    full_name = serializers.CharField(read_only=True, allow_null=True, allow_blank=True)
    email = serializers.CharField(read_only=True, allow_null=True, allow_blank=True)
    
    key = serializers.CharField(source="eid", read_only=True)
    # Acepted invitation in the external classroom system
    accepted_invitation = serializers.BooleanField(read_only=True)
    # Necesita conciliación, está inscrito en classroom, pero no como StudenEnrollment
    needs_conciliation = serializers.BooleanField(read_only=True)

    class Meta(LmsStudentEnrollmentBaseSerializer.Meta):
        fields = LmsStudentEnrollmentBaseSerializer.Meta.fields + ["key"]


class LmsStudentEnrollmentRetrieveSerializer(serializers.Serializer):
    enrollments = LmsStudentEnrollmentListSerializer(many=True, read_only=True)
    enrollments_count = serializers.IntegerField(read_only=True)
    accepted_invitation_count = serializers.IntegerField(read_only=True)

    class Meta:
        fields = [
            "enrollments",
            "enrollments_count",
            "accepted_invitation_count",
        ]
